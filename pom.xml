<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sankuai.meishi.stgy.algoplatform</groupId>
    <artifactId>algoplat-predictor</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>algoplat-predictor</name>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-parent</artifactId>
        <version>1.7.17.8</version>
    </parent>


    <modules>
        <module>predictor-client</module>
        <module>predictor-service</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.sankuai.mdp</groupId>
            <artifactId>mdp-boot-initializr-common</artifactId>
            <version>1.0.0</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>4.1.0</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan</groupId>
                <artifactId>poros-java-api-client</artifactId>
                <version>1.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.17.2</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>2.17.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
