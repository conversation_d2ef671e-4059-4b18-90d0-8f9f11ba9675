package com.sankuai.meishi.stgy.algoplatform.predictor.es.entity;

import java.util.*;

public class EsQueryRequest {

    private String index;
    private Integer from = 0;
    private Integer size = 10;

    // 查询条件
    private List<QueryCondition> mustConditions = new ArrayList<>();
    private List<QueryCondition> filterConditions = new ArrayList<>();

    // 向量查询
    private VectorQuery vectorQuery;

    public EsQueryRequest(String index) {
        this.index = index;
    }

    // 精确匹配
    public EsQueryRequest must(String field, Object value) {
        mustConditions.add(new QueryCondition(field, "term", value));
        return this;
    }

    // 全文搜索
    public EsQueryRequest match(String field, Object value) {
        mustConditions.add(new QueryCondition(field, "match", value));
        return this;
    }

    // 范围查询
    public EsQueryRequest range(String field, Object from, Object to) {
        QueryCondition condition = new QueryCondition(field, "range", null);
        if (from != null) condition.addParam("gte", from);
        if (to != null) condition.addParam("lte", to);
        mustConditions.add(condition);
        return this;
    }

    // 过滤条件
    public EsQueryRequest filter(String field, Object value) {
        filterConditions.add(new QueryCondition(field, "term", value));
        return this;
    }

    // 向量查询
    public EsQueryRequest vector(String field, float[] queryVector, int k) {
        this.vectorQuery = new VectorQuery(field, queryVector, k);
        return this;
    }

    // 分页
    public EsQueryRequest page(int from, int size) {
        this.from = from;
        this.size = size;
        return this;
    }

    // getter方法
    public String getIndex() { return index; }
    public Integer getFrom() { return from; }
    public Integer getSize() { return size; }
    public List<QueryCondition> getMustConditions() { return mustConditions; }
    public List<QueryCondition> getFilterConditions() { return filterConditions; }
    public VectorQuery getVectorQuery() { return vectorQuery; }
}
