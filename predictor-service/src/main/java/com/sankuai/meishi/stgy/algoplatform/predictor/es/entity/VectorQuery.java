package com.sankuai.meishi.stgy.algoplatform.predictor.es.entity;

public class VectorQuery {

    private String field;
    private float[] queryVector;
    private Integer k;

    public VectorQuery(String field, float[] queryVector, int k) {
        this.field = field;
        this.queryVector = queryVector;
        this.k = k;
    }

    public String getField() { return field; }
    public float[] getQueryVector() { return queryVector; }
    public Integer getK() { return k; }
}

