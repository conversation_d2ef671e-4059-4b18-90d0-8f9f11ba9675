package com.sankuai.meishi.stgy.algoplatform.predictor.config;


import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.sankuai.meishi.stgy.algoplatform.predictor.bo.AlgoConsumer;
import com.sankuai.meishi.stgy.algoplatform.predictor.bo.AlgoProducer;
import com.sankuai.meishi.stgy.algoplatform.predictor.bo.BmlMatchMafkaConfigs;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import com.sankuai.meishi.stgy.algoplatform.predictor.mq.BmlMatchAlgoRequestListener;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

@Component
@Slf4j
@DependsOn({"springContextUtils", "lionConfig"})
public class BmlMatchMafkaBeanConfig {

    @Autowired
    BmlMatchAlgoRequestListener bmlMatchAlgoRequestListener;

    public static final String BML_MATCH_MAFKA_PRODUCER_BEAN_NAME = "bmlMatchResultProducer";
    public static final String BML_MATCH_MAFKA_CONSUMER_BEAN_NAME = "bmlMatchResultConsumer";


    @PostConstruct
    public void init() throws Exception {
        BmlMatchMafkaConfigs bmlMatchMafkaConfigs = LionConfig.BML_MATCH_MAFKA_CONFIGS;
        AlgoProducer algoProducer = bmlMatchMafkaConfigs.getAlgoProducer();
        AlgoConsumer algoConsumer = bmlMatchMafkaConfigs.getAlgoConsumer();

        //初始化生产者bean
        initProducer(algoProducer);

        //初始化消费者bean
        initConsumer(algoConsumer);
    }

    private void initProducer(AlgoProducer algoProducer) throws Exception {
        if (algoProducer == null) {
            log.warn("当前机器未配置通用匹配系统生产者实例");
            Cat.logEvent("BmlMatchMafka", "MafkaProducerNotConfig");
            return;
        }

        if (StringUtils.isEmpty(algoProducer.getBgNameSpace()) ||
                StringUtils.isEmpty(algoProducer.getAppKey()) ||
                StringUtils.isEmpty(algoProducer.getTopicName())) {
            log.error("通用匹配系统生产者实例配置不完整");
            throw new RuntimeException("通用匹配系统生产者实例配置不完整" + JSON.toJSONString(algoProducer));
        }

        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, algoProducer.getBgNameSpace());
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, algoProducer.getAppKey());

        // 指定TopicSet发送消息
        if (isGrayReleaseCell(algoProducer.getCell())) {
            properties.setProperty(ConsumerConstants.CELL_NAME, algoProducer.getCell());
        }
        IProducerProcessor<Object, Object> producer = MafkaClient.buildProduceFactory(properties, algoProducer.getTopicName());
        if (producer == null) {
            RaptorTrack.Sys_UnexpectedVisitNum.report("BmlMatchMafkaProducerInitFail");
            log.error("BmlMatchMafkaBeanConfig.initProducer bean is null");
            throw new RuntimeException("生产者实例初始化失败.cellName=" + algoProducer.getCell());
        }

        SpringContextUtils.registerSingletonBean(BML_MATCH_MAFKA_PRODUCER_BEAN_NAME, producer);
    }

    private void initConsumer(AlgoConsumer algoConsumer) throws Exception {
        if (algoConsumer == null) {
            log.warn("当前机器未配置通用匹配系统消费者实例");
            Cat.logEvent("BmlMatchMafka", "MafkaConsumerNotConfig");
            return;
        }


        if (StringUtils.isEmpty(algoConsumer.getBgNameSpace()) ||
                StringUtils.isEmpty(algoConsumer.getAppKey()) ||
                StringUtils.isEmpty(algoConsumer.getSubscribeGroup()) ||
                StringUtils.isEmpty(algoConsumer.getTopicName())) {
            log.error("通用匹配系统消费者实例配置不完整");
            throw new RuntimeException("通用匹配系统消费者实例配置不完整" + JSON.toJSONString(algoConsumer));
        }

        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, algoConsumer.getBgNameSpace());
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, algoConsumer.getAppKey());
        properties.setProperty(ConsumerConstants.SubscribeGroup, algoConsumer.getSubscribeGroup());
        if (isGrayReleaseCell(algoConsumer.getCell())) {
            properties.setProperty(ConsumerConstants.CELL_NAME, algoConsumer.getCell());
        }

        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, algoConsumer.getTopicName());
        consumer.recvMessageWithParallel(String.class, bmlMatchAlgoRequestListener);
        SpringContextUtils.registerSingletonBean(BML_MATCH_MAFKA_CONSUMER_BEAN_NAME, consumer);
    }

    private boolean isGrayReleaseCell(String cellName) {
        return StringUtils.isNotEmpty(cellName)
                && !StringUtils.equalsIgnoreCase(cellName, "master")
                && !StringUtils.equalsIgnoreCase(cellName, "default")
                && !StringUtils.equalsIgnoreCase(cellName, "default-cell");
    }

}
