package com.sankuai.meishi.stgy.algoplatform.predictor.es.impl;

import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.json.JsonData;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.sankuai.meishi.stgy.algoplatform.predictor.es.EsClient;
import com.sankuai.meishi.stgy.algoplatform.predictor.es.EsService;
import com.sankuai.meishi.stgy.algoplatform.predictor.es.entity.EsQueryRequest;
import com.sankuai.meituan.poros.client.PorosApiClient;
import org.elasticsearch.action.search.SearchRequest;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class EsServiceImpl implements EsService {
    @Override
    public List<Map<String, Object>> complexQuery(String index, String queryJson) {
        EsQueryRequest esQueryRequest = JSONObject.parseObject(queryJson, EsQueryRequest.class);
        PorosApiClient esClient = EsClient.getEsClient();
        try {

        } catch (Exception e) {
            Cat.logError(e);
        }
        return Collections.emptyList();
    }
}
