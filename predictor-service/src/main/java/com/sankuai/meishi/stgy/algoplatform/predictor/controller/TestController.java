package com.sankuai.meishi.stgy.algoplatform.predictor.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dianping.cat.Cat;
import com.google.common.collect.ImmutableMap;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.*;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.*;
import com.sankuai.meishi.stgy.algoplatform.predictor.crane.MonitorTask;
import com.sankuai.meishi.stgy.algoplatform.predictor.crane.PythonProcessTask;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.BizStrategyPo;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.BizStrategyExample;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.mapper.BizStrategyMapper;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.PythonProfileService;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.PythonProfilingTask;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.JavaBridge;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.PythonInterpreter;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.PythonInterpreterFactory;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.SimpleFeatureService;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.SimplePredictService;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.TairClient;
import com.sankuai.meishi.stgy.algoplatform.predictor.thrift.TPredictServicePublish;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.UrlResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.net.MalformedURLException;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("test")
public class TestController {
    @Resource
    private AlgoPackageService algoPackageService;
    @Resource
    private SimplePredictService simplePredictService;
    @Resource
    private SimpleFeatureService simpleFeatureService;
    @Resource
    private TPredictServicePublish predictServicePublish;
    @Resource
    private PredictAppService predictAppService;
    @Resource
    private PredictionQueryAppService predictionQueryAppService;
    @Resource
    private PythonProfileService pythonProfileService;
    @Resource
    TPredictServicePublish tPredictServicePublish;

    @GetMapping(path = "pullCode")
    public String pullCode(String version, String codeRepo) {
        AlgoPackage a = new AlgoPackage();
        a.setVersion(version);
        a.setCodeRepo(codeRepo);
        a.pullCode();
        return "ok";
    }

    @GetMapping(path = "loadAlgoPackage")
    public String loadAlgoPackage(Long id) {
        algoPackageService.loadPackage(id);
        return "ok";
    }

    @PostMapping("batchPredictModel")
    public Object batchPredictModel(@RequestParam(required = false) Long modeVersion,
                                    @RequestParam(required = false) String signatureName,
                                    @RequestParam(required = false, defaultValue = "java.lang.Integer") String type,
                                    @RequestBody String body) throws Exception {
        long begin = System.currentTimeMillis();
        Class aClass = Class.forName(type);

        JSONArray jsonArray = JSON.parseArray(body);

        Map<String, Object> resultMap = new HashMap<>(jsonArray.size());

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject modelInput = jsonArray.getJSONObject(i);
            String model = modelInput.getString("model");
            String input = modelInput.getString("input");
            Map<String, List<?>> resp = simplePredictService.predict(model,
                    modeVersion, signatureName, aClass,
                    JSONObject.parseObject(input, new TypeReference<Map<String, List<List<Number>>>>() {
                    }, Feature.OrderedField));
            resultMap.put(model, resp);
        }
        return ImmutableMap.of("cost", System.currentTimeMillis() - begin,
                "data", resultMap);
    }

    @PostMapping(path = "invoke")
    public String invoke(String version,
                         String module,
                         String path,
                         String method,
                         @RequestParam(required = false, defaultValue = "python3.7_common") String runtime,
                         @RequestBody String json) {
        long begin = System.currentTimeMillis();
        AlgoPackage a = new AlgoPackage();
        a.setVersion(version);
        a.setModulePath(module);
        a.setRuntime(runtime);
        Map<String, Object> resp = a.invoke(path, method, JSONObject.parseObject(json, Map.class, Feature.OrderedField), JSONObject.parseObject("{}", Map.class));
        return JSONObject.toJSONString(ImmutableMap.of("cost", System.currentTimeMillis() - begin,
                "data", resp));
    }

    @PostMapping(path = "invokeDirectly")
    public String invokeDirectly(@RequestParam(defaultValue = "python3.7_common") String interpreter, String path, String method, @RequestBody String json) {
        long t0 = System.currentTimeMillis();
        String resp = PythonInterpreterFactory.getRuntime(interpreter).getInstance()
                .invoke(path, method, json, "");
        String result = JSONObject.toJSONString(ImmutableMap.of("cost", System.currentTimeMillis() - t0,
                "data", resp));
        Cat.logEvent("PredictorHttp", "invokeDirectly");
        return result;
    }

    @PostMapping(path = "invokeDirectlyWithPull")
    public String invokeDirectlyWithPull(@RequestParam(defaultValue = "python3.7_common") String interpreter, String path, String method, String commitId, String repo, @RequestBody String json) {
        try {
            AlgoPackage a = new AlgoPackage();
            a.setVersion(commitId);
            a.setCodeRepo(repo);
            a.pullCode();
            long t0 = System.currentTimeMillis();
            String resp = PythonInterpreterFactory.getRuntime(interpreter).getInstance()
                    .invoke(path, method, json, "");
            String result = JSONObject.toJSONString(ImmutableMap.of("cost", System.currentTimeMillis() - t0,
                    "data", resp));
            Cat.logEvent("PredictorHttp", "invokeDirectly");
            return result;
        } catch (Exception e) {
            Cat.logError(e);
            StringWriter sw = new StringWriter();
            e.printStackTrace(new PrintWriter(sw));
            return sw.toString();
        }
    }

    @PostMapping(path = "invokeDirectlyHttp")
    @ResponseBody
    public TInvokeDirectlyResponse invokeDirectlyHttp(@RequestBody TInvokeDirectlyRequest req)
            throws Exception {
        TInvokeDirectlyResponse response = predictServicePublish.invokeDirectly(req);
        Cat.logEvent("PredictorHttp", "invokeDirectlyHttp");
        return response;
    }

    @PostMapping("predictModel")
    @ResponseBody
    public String predictModel(@RequestParam String modelName,
                               @RequestParam(required = false) Long modeVersion,
                               @RequestParam(required = false) String signatureName,
                               @RequestParam(required = false, defaultValue = "java.lang.Integer") String type,
                               @RequestBody String input) throws Exception {
        long begin = System.currentTimeMillis();
        Class aClass = Class.forName(type);
        Map<String, List<?>> resp = simplePredictService.predict(modelName,
                modeVersion, signatureName, aClass,
                JSONObject.parseObject(input, new TypeReference<Map<String, List<List<Number>>>>() {
                }, Feature.OrderedField));
        return JSONObject.toJSONString(ImmutableMap.of("cost", System.currentTimeMillis() - begin,
                "data", resp));
    }

    @PostMapping("predictWithCache")
    @ResponseBody
    public String predictWithCache(@RequestBody String inputJson) {
        return JavaBridge.predictWithCache(inputJson);
    }

    @PostMapping("xgbPredictModel")
    @ResponseBody
    public String xgbPredictModel(@RequestParam String modelName,
                                  @RequestParam(required = false) Long modeVersion,
                                  @RequestParam(required = false) String signatureName,
                                  @RequestParam(required = false) String defaultValue,
                                  @RequestBody String input) throws Exception {
        long begin = System.currentTimeMillis();
        List<List<Double>> res = simplePredictService.xgbPredict(modelName, modeVersion, signatureName, JSONObject.parseObject(input, new TypeReference<List<Map<String, Double>>>() {
        }, Feature.OrderedField), defaultValue);
        return JSONObject.toJSONString(ImmutableMap.of("cost", System.currentTimeMillis() - begin,
                "data", res));
    }

    @PostMapping("xgbPredictModelByMatrix")
    @ResponseBody
    public String xgbPredictModelByMatrix(@RequestParam String modelName,
                                          @RequestBody String input) throws Exception {
        long begin = System.currentTimeMillis();
        List<List<Double>> res = simplePredictService.xgbPredictByMatrix(modelName, JSONObject.parseObject(input, new TypeReference<List<List<Double>>>() {
        }));
        return JSONObject.toJSONString(ImmutableMap.of("cost", System.currentTimeMillis() - begin,
                "data", res));
    }

    @PostMapping("queryFeature")
    @ResponseBody
    public String queryFeature(@RequestParam String groupId, @RequestBody String input) {
        long begin = System.currentTimeMillis();
        List<Map<String, String>> keys = JSONObject.parseObject(input, new TypeReference<List<Map<String, String>>>() {
        }, Feature.OrderedField);
        List<Map<String, String>> res = simpleFeatureService.query(groupId, keys);
        return JSONObject.toJSONString(ImmutableMap.of("cost", System.currentTimeMillis() - begin,
                "data", res));
    }

    @PostMapping(value = "predict", produces = {"text/plain;charset=UTF-8"})
    @ResponseBody
    public String predict(@RequestBody String input) {
        PredictContext context = JSONObject.parseObject(input, PredictContext.class, Feature.OrderedField);
        predictAppService.predict(context);
        return JSONObject.toJSONString(context);
    }

    @PostMapping(value = "predictThrift", produces = {"text/plain;charset=UTF-8"})
    @ResponseBody
    public String predictThrift(@RequestBody String input) throws TException {
        TPredictRequest req = JSONObject.parseObject(input, TPredictRequest.class, Feature.OrderedField);
        TPredictResponse resp = predictServicePublish.predict(req);
        return JSONObject.toJSONString(resp);
    }

    @PostMapping(value = "queryPredictionsByGroovyScript", produces = {"text/plain;charset=UTF-8"})
    @ResponseBody
    public String queryPredictionsByGroovyScript(@RequestParam String bizCode, @RequestParam List<String> entityIds,
                                                 @RequestParam String extra, @RequestBody String script) {
        PredictionQueryContext context = PredictionQueryContext.newInstance(bizCode, entityIds, JSONObject.parseObject(extra, Map.class));
        predictionQueryAppService.queryPredictionsByGroovyScript(script, context);
        return JSONObject.toJSONString(context);
    }

    @PostMapping(value = "queryPredictionsThrift", produces = {"text/plain;charset=UTF-8"})
    @ResponseBody
    public String queryPredictionsThrift(@RequestBody String input) throws TException {
        TPredictionRequest req = JSONObject.parseObject(input, TPredictionRequest.class, Feature.OrderedField);
        TPredictionResponse resp = predictServicePublish.queryPredictions(req);
        return JSONObject.toJSONString(resp);
    }

    @PostMapping("/readLion")
    @ResponseBody
    public String readLion(@RequestParam String key, @RequestBody String defaultValue) {
        return JavaBridge.readLion(key, defaultValue);
    }

    @GetMapping("/readTair")
    public Object readTair(String key) {
        return JSON.parse(JavaBridge.readTair(key));
    }

    @PostMapping("/logMetric")
    public void logMetric(String name, String tagsJson, int count) {
        JavaBridge.logMetric(name, tagsJson, count);
    }

    @Resource
    private TairClient tairClient;

    @PostMapping("writeTair")
    public Object writeTair(@RequestParam("file") MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            Map<String, String> map = new HashMap<>();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            while ((line = reader.readLine()) != null) {
                String[] split = line.split("\t");
                map.putIfAbsent(split[0], split[1]);
            }
            tairClient.batchPut(map, 7, TimeUnit.DAYS);
            return map;
        } catch (IOException e) {
            log.error("writeTair error", e);
            return "error";
        }
    }

    @PostMapping("tritonPredict")
    @ResponseBody
    public String tritonPredict(@RequestParam String modelName,
                                @RequestParam(required = false) Long modeVersion,
                                @RequestBody String input) throws Exception {
        long begin = System.currentTimeMillis();
        JSONObject jsonObject = JSONObject.parseObject(input);
        LinkedHashMap<String, List<Object>> output = simplePredictService.tritonPredict(modelName, modeVersion.toString(),
                jsonObject.getObject("inputValue", new TypeReference<LinkedHashMap<String, List<Object>>>() {
                }),
                jsonObject.getObject("inputShape", new TypeReference<Map<String, List<Integer>>>() {
                }),
                jsonObject.getObject("inputType", new TypeReference<Map<String, String>>() {
                }),
                jsonObject.getObject("outputType", new TypeReference<LinkedHashMap<String, String>>() {
                })
        );
        return JSONObject.toJSONString(ImmutableMap.of("cost", System.currentTimeMillis() - begin,
                "data", output));
    }

    @PostMapping("llmPredict")
    @ResponseBody
    public String llmPredict(@RequestParam String bizCode, @RequestBody String input) throws Exception {
        long begin = System.currentTimeMillis();
        JSONObject jsonObject = JSONObject.parseObject(input);
        Map<String, Object> output = simplePredictService.llmPredict(bizCode,
                jsonObject.getObject("prompts", new TypeReference<Map<String, String>>() {
                }),
                jsonObject.getObject("extra", new TypeReference<Map<String, String>>() {
                })
        );
        return JSONObject.toJSONString(ImmutableMap.of("cost", System.currentTimeMillis() - begin,
                "data", output));
    }

//    @PostMapping("invokeThrift")
//    public TPredictResponse invokeThrift(@RequestBody Map<String, String> req) throws Exception {
//        TPredictRequest request = new TPredictRequest();
//        request.setBizCode("langchain_custom");
//        request.setReq(req);
//        return predictServicePublish.predict(request);
//    }

    @PostMapping(value = "predictAsync", produces = {"text/plain;charset=UTF-8"})
    @ResponseBody
    public String predictAsync(@RequestBody String input) {
        TPredictRequest context = JSONObject.parseObject(input, TPredictRequest.class, Feature.OrderedField);
        try {
            tPredictServicePublish.predictAsync(context, false);

            return "0";
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return "-1";
        }
    }


    @GetMapping("invokePythonGC")
    @ResponseBody
    public String invokePythonGC(@RequestParam(required = false) String processName) {
        LinkedHashMap<String, String> res = new LinkedHashMap<>();
        if (StringUtils.isEmpty(processName)) {
            PythonInterpreterFactory.getAll()
                    .forEach(f -> {
                        res.put(f.getProcessName(), f.getInstance().run_gc());
                    });
        } else {
            PythonInterpreter p = PythonInterpreterFactory.getRuntimeByProcessName(processName);
            if (p != null) {
                res.put(p.getProcessName(), p.getInstance().run_gc());
            }
        }
        return JSONObject.toJSONString(res);
    }

    @GetMapping("showPythonMemory")
    @ResponseBody
    public String showPythonMemory(@RequestParam String processName, @RequestParam(required = false) Long objId) {
        PythonInterpreter p = PythonInterpreterFactory.getRuntimeByProcessName(processName);
        if (p != null) {
            if (p.getName().startsWith("pypy")) {
                return "pypy不支持";
            }
            return p.getInstance().show_memory(objId);
        }
        return "";
    }

    @Autowired
    private PythonProcessTask processTask;

    @GetMapping("restartPythonProcess")
    @ResponseBody
    public String restartPythonProcess() {
        processTask.pypyProcessRestart();
        return "ok";
    }

    @Autowired
    private BizStrategyMapper bizStrategyMapper;

    @GetMapping("testBizStrategyTableMigrate")
    @ResponseBody
    public String testBizStrategyTableMigrate() {
        List<BizStrategyPo> result = bizStrategyMapper.selectByExample(new BizStrategyExample());
        log.info("result size : {}, detail : {}", result.size(), JSON.toJSONString(result));
        return "ok";
    }

    @GetMapping("dumpStack")
    @ResponseBody
    public String dumpStack(@RequestParam String processName,
                            @RequestParam(required = false, defaultValue = "false") Boolean force) {
        List<String> stacks = pythonProfileService.dumpStack(processName, force);
        return JSONObject.toJSONString(ImmutableMap.of("output", stacks));
    }

    @GetMapping("recordProfiles")
    @ResponseBody
    public String recordProfiles(@RequestParam String processName,
                                 @RequestParam(required = false, defaultValue = "300") Integer duration,
                                 @RequestParam(required = false, defaultValue = "false") Boolean force) {
        PythonProfilingTask task = pythonProfileService.recordProfiles(processName, duration, force);
        return JSONObject.toJSONString(task, SerializerFeature.SortField);
    }

    @GetMapping("downloadProfiles")
    public ResponseEntity<org.springframework.core.io.Resource> downloadProfiles(@RequestParam String outputId) throws FileNotFoundException, MalformedURLException {
        File file = pythonProfileService.downloadProfiles(outputId);
        if (file == null) {
            return ResponseEntity.notFound().build();
        }
        UrlResource resource = new UrlResource(file.toURI());
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .header("Content-Disposition", "attachment; filename=\"" + file.getName() + "\"")
                .contentLength(file.length())
                .body(resource);
    }

    @Resource
    MonitorTask monitorTask;

    @GetMapping("nodeMonitor")
    public void nodeMonitor() {
        monitorTask.serverNodeMonitor();
    }

}
