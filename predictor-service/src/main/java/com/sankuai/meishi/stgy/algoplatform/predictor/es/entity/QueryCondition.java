package com.sankuai.meishi.stgy.algoplatform.predictor.es.entity;

import java.util.HashMap;
import java.util.Map;

public class QueryCondition {

    private String field;
    private String type;
    private Object value;
    private Map<String, Object> params = new HashMap<>();

    public QueryCondition(String field, String type, Object value) {
        this.field = field;
        this.type = type;
        this.value = value;
    }

    public QueryCondition addParam(String key, Object value) {
        this.params.put(key, value);
        return this;
    }

    public String getField() { return field; }
    public String getType() { return type; }
    public Object getValue() { return value; }
    public Map<String, Object> getParams() { return params; }
}

